# TopAppBar Component Specification

## Table of Contents

- [Overview](#overview)
- [Component States Matrix](#component-states-matrix)
- [Conditional Rendering Rules](#conditional-rendering-rules)
- [Data Requirements](#data-requirements)
- [Interaction Behaviors](#interaction-behaviors)
- [Responsive Design](#responsive-design)
- [Integration Points](#integration-points)
- [JRPG Design Elements](#jrpg-design-elements)

## Overview

The TopAppBar is the primary navigation component for the E-Senpai platform, featuring a JRPG-style interface with neobrutalism design principles. It provides contextual navigation, user status information, and quick access to key platform features.

### Core Features

- **Token Display**: Prominent soda and cap token balances with JRPG-style icons
- **Notification System**: Bell icon with unread count badge
- **Order Management**: Quick access to order history and active orders
- **User Avatar**: Dropdown menu with role-specific options
- **Contextual Elements**: Logo, back button, and search (browse page only)
- **Responsive Design**: Adapts to mobile and desktop layouts

## Component States Matrix

### User Authentication States

| State             | Description        | Available Elements                     |
| ----------------- | ------------------ | -------------------------------------- |
| `unauthenticated` | User not logged in | Logo, Login Button, Locale Switcher    |
| `authenticated`   | User logged in     | All elements based on role and context |

### User Roles & Capabilities

| Role            | Capabilities              | Special Menu Items                               |
| --------------- | ------------------------- | ------------------------------------------------ |
| `customer`      | Basic platform access     | Profile, Settings, KYC (if incomplete)           |
| `provider`      | Service management        | Provider Dashboard, Service Management           |
| `admin`         | Platform administration   | Admin Panel, User Management, Content Management |
| `support_agent` | Support ticket management | Support Dashboard, Ticket Queue                  |

### KYC Verification States

| Status     | Description  | Menu Impact               |
| ---------- | ------------ | ------------------------- |
| `draft`    | Not started  | Show "Complete KYC" link  |
| `pending`  | Under review | Show "KYC Pending" status |
| `approved` | Verified     | Hide KYC-related items    |
| `rejected` | Rejected     | Show "Retry KYC" link     |

### Page Context States

| Page Context | Search Bar | Back Button | Special Elements    |
| ------------ | ---------- | ----------- | ------------------- |
| `home`       | Hidden     | Hidden      | Full logo           |
| `browse`     | Visible    | Hidden      | Search with filters |
| `profile`    | Hidden     | Visible     | User context        |
| `orders`     | Hidden     | Visible     | Order filters       |
| `settings`   | Hidden     | Visible     | Settings context    |
| `admin`      | Hidden     | Visible     | Admin tools         |

## Conditional Rendering Rules

### Token Balances Display

```typescript
// Show token balances for authenticated users only
if (user.isAuthenticated) {
  return (
    <div className="flex gap-2">
      <TokenBalance type="soda" amount={wallet.soda_balance} />
      <TokenBalance type="cap" amount={wallet.cap_balance} />
    </div>
  );
}
```

### Notification Badge

```typescript
// Show notification badge with count
if (user.isAuthenticated && notifications.unreadCount > 0) {
  return (
    <Button variant="neutral" size="icon" className="relative">
      <Bell className="size-4" />
      <Badge className="absolute -top-1 -right-1 min-w-5 h-5">
        {notifications.unreadCount > 99 ? '99+' : notifications.unreadCount}
      </Badge>
    </Button>
  );
}
```

### User Avatar Dropdown Menu

```typescript
// Role-specific menu items
const getMenuItems = (user: User) => {
  const baseItems = [
    { label: "Profile", href: "/profile" },
    { label: "Settings", href: "/settings" }
  ];

  // KYC verification link
  if (user.kyc_status !== "approved") {
    baseItems.push({
      label: user.kyc_status === "rejected" ? "Retry KYC" : "Complete KYC",
      href: "/kyc"
    });
  }

  // Role-specific items
  if (user.roles.includes("provider")) {
    baseItems.push({ label: "Provider Dashboard", href: "/provider" });
  }

  if (user.roles.includes("admin")) {
    baseItems.push({ label: "Admin Panel", href: "/admin" });
  }

  if (user.roles.includes("support_agent")) {
    baseItems.push({ label: "Support Dashboard", href: "/support" });
  }

  baseItems.push({ label: "Logout", action: "logout" });
  return baseItems;
};
```

### Search Bar (Browse Page Only)

```typescript
// Show search bar only on browse page
if (currentPage === 'browse') {
  return (
    <div className="flex-1 max-w-md">
      <SearchInput
        placeholder="Search providers, activities..."
        onSearch={handleSearch}
        filters={['activity', 'provider', 'price']}
      />
    </div>
  );
}
```

## Data Requirements

### User Data

```typescript
interface UserData {
  id: string;
  username: string;
  avatar_url?: string;
  roles: ("customer" | "provider" | "admin" | "support_agent")[];
  kyc_status: "draft" | "pending" | "approved" | "rejected";
  is_banned: boolean;
}
```

### Wallet Data

```typescript
interface WalletData {
  soda_balance: number;
  cap_balance: number;
}
```

### Notification Data

```typescript
interface NotificationData {
  unreadCount: number;
  recent: Array<{
    id: string;
    title: string;
    message: string;
    created_at: string;
    read_at?: string;
  }>;
}
```

### Order Data

```typescript
interface OrderData {
  activeOrdersCount: number;
  pendingOrdersCount: number;
  recentOrders: Array<{
    id: string;
    status: OrderStatus;
    created_at: string;
  }>;
}
```

## Interaction Behaviors

### Token Balance Click

- **Soda Balance**: Navigate to `/wallet` with soda tab active
- **Cap Balance**: Navigate to `/wallet` with cap tab active
- **Hover Effect**: Show tooltip with token description

### Notification Bell Click

- **Single Click**: Open notification dropdown
- **Badge Click**: Mark all as read and open notifications page
- **Outside Click**: Close dropdown

### Orders Button Click

- **Customer Role**: Navigate to `/orders` (customer view)
- **Provider Role**: Navigate to `/orders` (provider view)
- **Badge**: Show count of pending orders requiring attention

### Avatar Dropdown

- **Click**: Toggle dropdown menu
- **Menu Item Click**: Navigate to respective page or execute action
- **Logout**: Clear session and redirect to home

### Search Bar (Browse Page)

- **Input Change**: Debounced search with 300ms delay
- **Filter Selection**: Update search parameters
- **Clear**: Reset search and filters

## Responsive Design

### Desktop Layout (≥1024px)

```
[Logo] [Search?] [Spacer] [Soda] [Cap] [Notifications] [Orders] [Avatar]
```

### Tablet Layout (768px - 1023px)

```
[Logo] [Search?] [Spacer] [Tokens] [Notifications] [Orders] [Avatar]
```

### Mobile Layout (<768px)

```
[Back/Logo] [Search?] [Spacer] [Notifications] [Orders] [Avatar]
```

**Mobile Adaptations:**

- Token balances moved to dropdown menu
- Compact button sizes
- Simplified search interface
- Collapsible elements

## Integration Points

### Next.js Integration

```typescript
import { Link, useRouter, usePathname } from "shared/lib";
import { useLocale, useTranslations } from "next-intl";
```

### Supabase Integration

```typescript
// Real-time wallet updates
const { data: wallet } = useSupabaseQuery(
  supabase
    .schema("app_transaction")
    .from("wallet")
    .select("soda_balance, cap_balance")
    .eq("user_id", user.id)
    .single()
);

// Real-time notification updates
const { data: notifications } = useSupabaseQuery(
  supabase
    .schema("app_account")
    .from("notification")
    .select("*")
    .eq("recipient_id", user.id)
    .is("read_at", null)
);
```

### Shared Components

```typescript
import {
  Avatar,
  AvatarImage,
  AvatarFallback,
  Button,
  Badge,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "shared/components/ui";
```

## JRPG Design Elements

### Visual Style

- **Neobrutalism**: Bold borders, shadow effects, high contrast
- **Pastel Colors**: Mustard yellow primary, cream background
- **Typography**: Noto Sans with bold weights (600/800)
- **Shadows**: 3px offset shadows for depth

### Token Display

```typescript
const TokenBalance = ({ type, amount }: { type: 'soda' | 'cap', amount: number }) => (
  <Button variant="neutral" className="gap-2 font-bold">
    <TokenIcon type={type} className="size-5" />
    <span className="text-sm">{formatTokenAmount(amount)}</span>
  </Button>
);
```

### Animation Effects

- **Hover States**: Translate shadow effects
- **Loading States**: Skeleton placeholders
- **Transitions**: 200ms ease-in-out
- **Badge Animations**: Pulse for new notifications

### Icon System

- **Soda Token**: Bottle/drink icon with golden color
- **Cap Token**: Star/gem icon with purple accent
- **Notifications**: Bell with animated badge
- **Orders**: Shopping bag with status indicators

### Accessibility

- **ARIA Labels**: Descriptive labels for all interactive elements
- **Keyboard Navigation**: Full keyboard support
- **Screen Readers**: Proper semantic markup
- **Focus States**: Visible focus indicators
- **Color Contrast**: WCAG AA compliance

## Implementation Example

### Component Structure

```typescript
interface TopAppBarProps {
  user?: UserData;
  wallet?: WalletData;
  notifications?: NotificationData;
  orders?: OrderData;
  currentPage: PageContext;
  onSearch?: (query: string, filters: SearchFilters) => void;
  onNavigate?: (path: string) => void;
  onLogout?: () => void;
}

export function TopAppBar({
  user,
  wallet,
  notifications,
  orders,
  currentPage,
  onSearch,
  onNavigate,
  onLogout
}: TopAppBarProps) {
  const t = useTranslations("TopAppBar");
  const router = useRouter();
  const pathname = usePathname();

  // Component implementation...
}
```

### Layout Container

```typescript
// Main container with responsive layout
<header className="flex items-center justify-between p-4 bg-background border-b-2 border-border shadow-shadow">
  {/* Left section */}
  <div className="flex items-center gap-4">
    {renderLogoOrBack()}
    {currentPage === 'browse' && renderSearchBar()}
  </div>

  {/* Right section */}
  <div className="flex items-center gap-2">
    {user && renderTokenBalances()}
    {user && renderNotificationButton()}
    {user && renderOrdersButton()}
    {user ? renderUserAvatar() : renderLoginButton()}
  </div>
</header>
```

### State Management

```typescript
// Real-time data subscriptions
useEffect(() => {
  if (!user) return;

  // Subscribe to wallet changes
  const walletSubscription = supabase
    .schema("app_transaction")
    .from("wallet")
    .on("UPDATE", { filter: `user_id=eq.${user.id}` }, (payload) => {
      setWallet(payload.new);
    })
    .subscribe();

  // Subscribe to notification changes
  const notificationSubscription = supabase
    .schema("app_account")
    .from("notification")
    .on("INSERT", { filter: `recipient_id=eq.${user.id}` }, () => {
      refetchNotifications();
    })
    .subscribe();

  return () => {
    walletSubscription.unsubscribe();
    notificationSubscription.unsubscribe();
  };
}, [user?.id]);
```

## Testing Strategy

### Unit Tests

```typescript
describe('TopAppBar', () => {
  it('shows login button for unauthenticated users', () => {
    render(<TopAppBar currentPage="home" />);
    expect(screen.getByText('Login')).toBeInTheDocument();
  });

  it('shows token balances for authenticated users', () => {
    render(
      <TopAppBar
        user={mockUser}
        wallet={{ soda_balance: 1000, cap_balance: 500 }}
        currentPage="home"
      />
    );
    expect(screen.getByText('1,000')).toBeInTheDocument();
    expect(screen.getByText('500')).toBeInTheDocument();
  });

  it('shows search bar only on browse page', () => {
    const { rerender } = render(
      <TopAppBar user={mockUser} currentPage="home" />
    );
    expect(screen.queryByPlaceholderText(/search/i)).not.toBeInTheDocument();

    rerender(<TopAppBar user={mockUser} currentPage="browse" />);
    expect(screen.getByPlaceholderText(/search/i)).toBeInTheDocument();
  });

  it('shows role-specific menu items', () => {
    render(
      <TopAppBar
        user={{ ...mockUser, roles: ['admin'] }}
        currentPage="home"
      />
    );
    fireEvent.click(screen.getByRole('button', { name: /avatar/i }));
    expect(screen.getByText('Admin Panel')).toBeInTheDocument();
  });
});
```

### Integration Tests

```typescript
describe("TopAppBar Integration", () => {
  it("updates token balances in real-time", async () => {
    // Test real-time wallet updates
  });

  it("navigates correctly on menu item clicks", async () => {
    // Test navigation behavior
  });

  it("handles search functionality", async () => {
    // Test search integration
  });
});
```

## Performance Considerations

### Optimization Strategies

1. **Memoization**: Use `React.memo` for expensive components
2. **Debouncing**: Search input with 300ms delay
3. **Lazy Loading**: Avatar images with loading states
4. **Caching**: Token balances and notification counts
5. **Subscription Management**: Proper cleanup of real-time subscriptions

### Bundle Size

- **Core Component**: ~15KB gzipped
- **Dependencies**: Shared UI components, icons
- **Code Splitting**: Lazy load dropdown menus

## Accessibility Compliance

### WCAG 2.1 AA Standards

- **Color Contrast**: 4.5:1 minimum ratio
- **Keyboard Navigation**: Tab order and focus management
- **Screen Readers**: Semantic HTML and ARIA labels
- **Motion**: Respect `prefers-reduced-motion`

### Implementation

```typescript
// ARIA labels and roles
<button
  aria-label={t('notifications.button.label')}
  aria-describedby="notification-count"
  role="button"
>
  <Bell className="size-4" />
  {unreadCount > 0 && (
    <Badge id="notification-count" aria-live="polite">
      {unreadCount}
    </Badge>
  )}
</button>
```

## Future Enhancements

### Phase 2 Features

1. **Voice Commands**: JRPG-style voice navigation
2. **Themes**: Multiple color schemes
3. **Animations**: Enhanced micro-interactions
4. **Shortcuts**: Keyboard shortcuts for power users
5. **Customization**: User-configurable layout

### Technical Debt

1. **Performance Monitoring**: Add metrics tracking
2. **Error Boundaries**: Graceful error handling
3. **Internationalization**: Complete translation coverage
4. **Documentation**: Interactive component playground

This comprehensive specification provides all the necessary details for implementing a robust, accessible, and user-friendly TopAppBar component that serves as the primary navigation interface for the E-Senpai platform while maintaining the distinctive JRPG aesthetic and neobrutalism design principles.
