# TopAppBar Component Specification

## Table of Contents

- [Component Parts](#component-parts)
- [User States](#user-states)
- [Page Context Behavior](#page-context-behavior)
- [Role-Based Features](#role-based-features)
- [Data Requirements](#data-requirements)

## Component Parts

### Logo/Back Button

- **Logo**: Shows on home page and when no back navigation needed
- **Back Button**: Shows on all other pages for navigation

### Search Bar

- **Visibility**: Only appears on browse page
- **Function**: Search providers and activities

### Token Balances

- **Soda Balance**: Shows user's soda token count
- **Cap Balance**: Shows user's cap token count
- **Visibility**: Only for authenticated users

### Notification Button

- **Bell Icon**: Shows notification access
- **Badge**: Displays unread notification count
- **Visibility**: Only for authenticated users

### Orders Button

- **Icon**: Shows order management access
- **Badge**: Displays pending orders count
- **Visibility**: Only for authenticated users

### User Avatar

- **Avatar Image**: User's profile picture or fallback
- **Dropdown Menu**: Role-specific menu options
- **Visibility**: Only for authenticated users

### Login Button

- **Visibility**: Only for unauthenticated users
- **Function**: Navigate to login page

---

## User States

### Authentication Status

- **Unauthenticated**: Shows logo and login button only
- **Authenticated**: Shows all user-specific components

### User Roles

- **Customer**: Basic access to orders, wallet, notifications
- **Provider**: Additional provider dashboard access
- **Admin**: Additional admin panel access
- **Support Agent**: Additional support dashboard access

### KYC Status

- **Draft/Pending**: Shows KYC completion link in avatar menu
- **Approved**: Hides KYC-related menu items
- **Rejected**: Shows retry KYC link in avatar menu

---

## Page Context Behavior

### Home Page

- **Logo**: Visible
- **Back Button**: Hidden
- **Search Bar**: Hidden

### Browse Page

- **Logo**: Visible
- **Back Button**: Hidden
- **Search Bar**: Visible

### Other Pages (Profile, Orders, Settings, Admin)

- **Logo**: Hidden
- **Back Button**: Visible
- **Search Bar**: Hidden

---

## Role-Based Features

### Avatar Dropdown Menu Items

#### All Authenticated Users

- Profile
- Settings
- Logout

#### Customer Role

- No additional items

#### Provider Role

- Provider Dashboard
- Service Management

#### Admin Role

- Admin Panel
- User Management

#### Support Agent Role

- Support Dashboard
- Ticket Queue

### KYC-Related Menu Items

- **Incomplete KYC**: "Complete KYC" or "Retry KYC" link
- **Complete KYC**: No KYC-related items

---

## Data Requirements

### User Data

- `id`: User identifier
- `username`: Display name
- `avatar_url`: Profile picture URL (optional)
- `roles`: Array of user roles
- `kyc_status`: KYC verification status
- `is_banned`: Ban status

### Wallet Data

- `soda_balance`: Soda token count
- `cap_balance`: Cap token count

### Notification Data

- `unreadCount`: Number of unread notifications

### Order Data

- `activeOrdersCount`: Number of active orders
- `pendingOrdersCount`: Number of pending orders requiring attention
