{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_42788bc2._.js", "server/edge/chunks/41428_@formatjs_intl-localematcher_lib_d637b060._.js", "server/edge/chunks/4294a_@supabase_auth-js_dist_module_51b8369e._.js", "server/edge/chunks/node_modules__pnpm_0c0ce5b1._.js", "server/edge/chunks/[root-of-the-server]__569f5a63._.js", "server/edge/chunks/packages_webapp_edge-wrapper_1a52fb86.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|ingest|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "oKH2XG+pncrIx4/k4x1NQVrid0DXNmC2fOUTyxzd+Sk=", "__NEXT_PREVIEW_MODE_ID": "1c1459d44e121e37425a7ef5db05e62e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "521e96deeecb71537d719d7267cf1437daea867b5bc656cd296d33613e10ec57", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "5924c492f592fc76f74aa77a1a05de6b33e2e98c3cee39e7b56eb1280f277f00"}}}, "instrumentation": null, "functions": {}}