# Advanced Profile Customization & Unique "Cap" Utilities

"Caps" are a non-withdrawable, engagement-focused currency. Their purpose is to reward users for their activity and deepen their interaction with the platform without impacting the core "Soda" economy. This document outlines suggestions for their use.

---

### I. Advanced Profile Customization (Using Caps)

This category focuses on giving users a deep sense of ownership and identity over their profiles. Instead of offering a few premium themes, Caps allow for granular, incremental customization.

#### 1. Granular Cosmetic Unlocks

Users can spend Caps to buy individual cosmetic components, allowing for greater personalization.

- **What it is:**
  - **Profile Borders:** Purchase specific border designs or color packs (e.g., "Fire Border," "Neon Blue Border").
  - **Username Effects:** Unlock special text effects for their username, like color gradients, a subtle glow, or custom fonts.
  - **Background Images:** Buy the right to use specific, pre-approved high-quality images or patterns as their profile banner. This maintains quality and safety.
  - **"Stickers" & "Flairs":** Purchase small, cosmetic icons or "stickers" that they can place in designated spots on their profile banner, similar to decorating a physical space.

#### 2. Profile "Showcase" Expansions

Give users the ability to show off what makes them proud.

- **What it is:** By default, a user profile might have a limited number of "showcase" slots (e.g., display your top 3 badges). Users can spend Caps to unlock more.
  - **Unlock More Badge Slots:** "Purchase a 4th badge slot for 500 Caps."
  - **Unlock a "Featured Review" Slot:** "Spend 1000 Caps to unlock a special section on your profile to permanently pin your favorite review."
  - **Unlock a "Top Supporter" Showcase:** A provider can unlock a section to showcase the users who have tipped or gifted them the most.
- **Why it works:** It directly ties the gamification system (earning badges) to a tangible reward (showing them off), encouraging further engagement to earn more things to display.

#### 3. Temporary Profile Effects

Create repeatable Cap sinks that offer high-impact, temporary visual flair.

- **What it is:** A user can spend Caps to activate a temporary, eye-catching animation on their profile.
  - Examples: "Activate a 'Falling Snow' effect for 24 hours for 150 Caps," or "Get a 'Golden Glow' on your avatar for the weekend for 200 Caps."
- **Why it works:** These are fun, highly visible, and not permanent, making them a great way for users to spend small amounts of Caps and for the platform to keep the currency circulating.

---

### II. Other Unique Utilities for "Caps"

This is where Caps can be integrated into the platform's core loops to create novel social and economic interactions.

#### 1. Interaction & Gifting Enhancements

Add a layer of fun and intrigue to social transactions.

- **What it is:**
  - **"Anonymity Cloak" for Gifts:** When a user sends a gift (paid for with Soda), they can spend an additional amount of _Caps_ to make the gift anonymous or appear as from a "Secret Admirer."
  - **Custom Gift Wrapping:** Spend Caps to wrap a gifted item in special "wrapping paper" that the recipient has to "open" with an animation.
- **Why it works:** It adds a playful social dynamic to the gifting feature, encouraging its use.

#### 2. Gamified & Luck-Based Systems

Leverage the psychological pull of variable rewards to create a powerful Cap sink.

- **What it is:**
  - **"Gacha" for Cosmetics (Loot Boxes):** Users spend Caps to "roll" for a random cosmetic item (a profile border, a chat sticker, an emoji pack). You can create different tiers of rarity (Common, Rare, Epic), making it exciting to land a high-value item for a low cost.
  - **Raffle Entries:** The platform can host weekly or monthly raffles for valuable prizes (e.g., a large bundle of Soda, a gift card for a new game, a free session with a top provider). Each raffle ticket costs a certain amount of Caps.
- **Why it works:** This is a highly effective and popular mechanic for driving engagement and providing a fun way for users to spend their earned Caps.

#### 3. Community-Driven Platform Development

Make users feel invested in the platform's future.

- **What it is:**
  - **"Fund" a Feature:** On your public roadmap or feature suggestion list, allow users to pledge their Caps to the features they want to see developed most.
  - **Vote on Community Events:** Let users spend Caps to vote on the next community event (e.g., "Should our next event be a Valorant tournament or a Minecraft building contest?").
- **Why it works:** This gives you invaluable data on what your users truly want, and it makes the community feel heard and empowered, leading to higher loyalty.

#### 4. Order & Search Enhancements

Allow users to use Caps to subtly influence the platform's systems.

- **What it is:**
  - **"Bump" a Review:** A _provider_ who receives a fantastic review can spend Caps to "bump" it to the top of their review list for a week, ensuring potential customers see it first.
  - **"Priority Request" Nudge:** A _customer_ can attach a small number of Caps to a new order. This doesn't change the price but acts as a visual signal to the provider that the customer is very keen. The Caps are consumed in the process.
- **Why it works:** It gives both providers and customers a small degree of agency over their visibility and interactions, powered by their engagement on the platform.
